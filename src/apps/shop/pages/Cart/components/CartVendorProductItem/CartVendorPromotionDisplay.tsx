import { Badge } from '@/libs/ui/Badge/Badge';
import { OfferType, ProductType } from '@/types';
import { getPriceString } from '@/utils';
import { getPromotionDetails } from './promotionUtils';

type CartVendorPromotionDisplayProps = {
  product: ProductType;
  offer: OfferType;
  quantity: number;
  productOfferId: string;
};

export const CartVendorPromotionDisplay = ({
  product,
  offer,
  quantity,
  productOfferId,
}: CartVendorPromotionDisplayProps) => {
  const { freeQty, freeOffer } = getPromotionDetails({
    quantity,
    product,
    offerId: productOfferId,
  });

  if (!freeOffer) return null;

  return (
    <div className="grid w-full gap-1 py-3">
      <div className="flex items-center gap-2">
        <Badge className="text-sxs h-5 w-7 rounded-sm bg-[#B6F5F959] px-0 py-0">
          {quantity}
        </Badge>
        <span className="flex-1 text-xs font-medium">{product.name}</span>
        <span className="text-xs font-medium text-neutral-500">
          {getPriceString(offer.price)}
        </span>
      </div>
      {freeQty > 0 && (
        <div className="flex items-center gap-2">
          <Badge className="text-sxs h-5 w-7 rounded-sm bg-[#B6F5F959] px-0 py-0">
            {freeQty}
          </Badge>
          <span className="flex-1 text-xs font-medium">{freeOffer.name}</span>
          <span className="text-xs font-medium text-green-700/90">Free</span>
        </div>
      )}
    </div>
  );
};
