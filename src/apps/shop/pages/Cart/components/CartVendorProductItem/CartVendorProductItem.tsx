import { Divider, Text, Title, Tooltip } from '@mantine/core';
import { Flex } from '@/libs/ui/Flex/Flex';
import { getPriceString } from '@/utils';
import { FEATURE_FLAGS, MODAL_NAME } from '@/constants';
import { Link } from 'react-router-dom';
import { getProductUrl } from '@/apps/shop/routes/utils';
import { PurchaseHistory } from '@/libs/products/components/PurchaseHistory/PurchaseHistory';
import { useModalStore } from '@/apps/shop/stores/useModalStore';
import { VendorSwap } from '@/libs/products/components/VendorSwap/VendorSwap';
import { useCartStore } from '@/apps/shop/stores/useCartStore/useCartStore';
import { ProductCartHorizontal } from '@/libs/products/components/ProductCardHorizontal/ProductCartHorizontal';
import styles from './CartVendorProductItem.module.css';
import MultiplyOperatorIcon from './assets/multiply-operator.svg?react';
import type { CartItemType } from '@/libs/cart/types';
import { AddToCartInput } from '@/libs/products/components/AddToCartInput/AddToCartInput';
import { useAsyncRequest } from '@/libs/api/hooks/useAsyncRequest/useAsyncRequest';
import { useBoxLoading } from '@/apps/shop/stores/useBoxLoadingStore';
import { Icon } from '@/libs/icons/Icon';
import { Button } from '@/libs/ui/Button/Button';

type CartVendorProductItemProps = {
  data: CartItemType;
};
export const CartVendorProductItem = ({ data }: CartVendorProductItemProps) => {
  const { id, quantity, product, subtotal, productOfferId } = data;
  const offer = product.offers.find(({ id }) => productOfferId === id);

  const { openModal } = useModalStore();
  const { addToCart, swapOfferCartItem } = useCartStore();
  const { toggleBoxLoading } = useBoxLoading();

  const handleOpenRemoveProductModal = () => {
    openModal({
      name: MODAL_NAME.REMOVE_PRODUCT_FROM_CART,
      product: product,
      productOfferId,
    });
  };

  const { apiRequest: swapVendor } = useAsyncRequest({
    apiFunc: async (productOfferId) =>
      swapOfferCartItem(id, productOfferId as string),
  });

  const handleSelectSwapVendor = (value: string | null) => {
    toggleBoxLoading('#cart-layout', () => swapVendor(value));
  };

  if (!offer) {
    return null;
  }

  const subtotalWithoutDiscount = +offer.price! * (data.quantity || 1);
  const productUrl = getProductUrl(product.id, productOfferId);

  return (
    <ProductCartHorizontal
      productOfferId={productOfferId}
      product={product}
      content={
        <Flex pl="md" direction="column">
          <Link to={productUrl} className={styles.titleWrap}>
            <Title order={3}>{product.name}</Title>
          </Link>
          <Flex align="center" mt="0.25rem" mb="md">
            <Text c="#666" size="xs">
              SKU:
              <Text ml="2px" fw="500" c="#344054" span>
                {offer.vendorSku}
              </Text>
            </Text>
            {product.manufacturer ? (
              <>
                <Divider orientation="vertical" h="1rem" mx="md" />
                <Text ml="2px" c="#344054" size="xs">
                  {product.manufacturer}
                </Text>
              </>
            ) : null}
          </Flex>
          <Flex direction="column" gap="0.25rem" maw="260px">
            <VendorSwap
              offers={product.offers}
              currentOfferId={productOfferId}
              onSwap={handleSelectSwapVendor}
            />

            <div className="my-3 w-full">
              <PurchaseHistory
                productId={offer.id}
                lastOrderedAt={offer.lastOrderedAt}
                lastOrderedQuantity={offer.lastOrderedQuantity}
              />
            </div>
          </Flex>
        </Flex>
      }
      actions={
        <Flex direction="column">
          {quantity && (
            <>
              <Flex>
                <Text
                  c="rgba(102, 102, 102, 0.80)"
                  size="0.75rem"
                  ta="right"
                  w="100%"
                  mb="0.5rem"
                  fw="500"
                  className={styles.unitPrice}
                >
                  Unit Price:{' '}
                  <Text size="0.75rem" c="#333" fw="500" span>
                    {getPriceString(offer.clinicPrice || offer.price)}
                  </Text>
                </Text>
                <MultiplyOperatorIcon />
              </Flex>
              <AddToCartInput
                originalAmount={quantity}
                minIncrement={offer.increments}
                onUpdate={({ amount: newQuantity, setError }) =>
                  addToCart({
                    productOfferId,
                    quantity: newQuantity,
                    onError: (message) => {
                      setError(message);
                    },
                  })
                }
              />
            </>
          )}

          <Flex
            justify="flex-end"
            align="flex-end"
            gap="10"
            mb="0.75rem"
            mt="0.5rem"
          >
            {subtotal && subtotalWithoutDiscount > +subtotal ? (
              <Text
                size="sm"
                fw="500"
                td="line-through"
                c="rgba(51, 51, 51, 0.50)"
              >
                {getPriceString(subtotalWithoutDiscount)}
              </Text>
            ) : null}
            <Text size="1.5rem" fw="500">
              {getPriceString(subtotal)}
            </Text>
          </Flex>
          <div className="flex items-center justify-end gap-4">
            {FEATURE_FLAGS.SAVED_ITEMS && (
              <>
                <Tooltip label="Save item for later">
                  <Button variant="unstyled" aria-label="Save item for later">
                    <Icon
                      name="saveFlag"
                      color="#667085"
                      size="1rem"
                      className="transition-colors hover:text-[#447bfd]"
                      aria-hidden={true}
                    />
                  </Button>
                </Tooltip>
                <div className="h-4 w-px bg-[#D0D5DD]" />
              </>
            )}
            <Tooltip label="Remove item from cart">
              <Button
                onClick={handleOpenRemoveProductModal}
                variant="unstyled"
                aria-label="Remove item from cart"
              >
                <Icon
                  name="trash"
                  color="#667085"
                  size="1rem"
                  aria-hidden={true}
                />
              </Button>
            </Tooltip>
          </div>
        </Flex>
      }
    />
  );
};
