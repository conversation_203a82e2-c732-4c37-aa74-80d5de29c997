import { OfferType, ProductType } from '@/types';
import { BenefitType, PromoType, RequirementType } from '@/types/common';

export function getPromotionDetails({
  quantity,
  product,
  offerId,
}: {
  quantity: number;
  product: ProductType;
  offerId: string;
  subtotal: number;
}) {
  let totalProducts = quantity;
  let freeQty = 0;
  let minReq: RequirementType | undefined;
  let minQty = 0;
  let freeBenefit: BenefitType | undefined;
  let freeOffer: OfferType | undefined;

  // Get promotions from product
  const promotions: PromoType[] = product.promotions || [];
  const promo = promotions.find((p) => p.type === 'buy_x_get_y');
  if (promo) {
    minReq = promo.requirements?.find(
      (requirement) => requirement.type === 'minimum_quantity',
    );
    minQty = minReq ? Number(minReq.minimumQuantity) : 0;
    freeBenefit = promo.benefits?.find(
      (benefit) => benefit.type === 'give_free_product',
    );
    const freeQtyPerTrigger = freeBenefit?.quantity
      ? Number(freeBenefit.quantity)
      : 0;
    const triggers = minQty > 0 ? Math.floor(quantity / minQty) : 0;
    freeQty = triggers * freeQtyPerTrigger;
    totalProducts += freeQty;
    const matchedOffer = product.offers.find((offer) => offer.id === offerId);
    freeOffer = freeBenefit?.freeProductOffer ?? matchedOffer;

    const savings = 0;
  }

  return {
    totalProducts,
    freeQty,
    minReq,
    minQty,
    freeBenefit,
    freeOffer,
  };
}
