import { Badge } from '@/libs/ui/Badge/Badge';
import { OfferType } from '@/types';
import { getPriceString } from '@/utils';
import { CartItemType } from '@/libs/cart/types';

type CartVendorPromotionDisplayProps = {
  item: CartItemType & {
    freeItemsQty: number;
    freeOffer: OfferType | null;
  };
};

export const CartVendorPromotionDisplay = ({
  item,
}: CartVendorPromotionDisplayProps) => {
  return (
    <div className="grid w-full gap-1 py-3">
      <div className="flex items-center gap-2">
        <Badge className="text-sxs h-5 w-7 rounded-sm bg-[#B6F5F959] px-0 py-0">
          {item.quantity}
        </Badge>
        <span className="flex-1 text-xs font-medium">{item.product.name}</span>
        <span className="text-xs font-medium text-neutral-500">
          {getPriceString(item.subtotal)}
        </span>
      </div>
      {item.freeItemsQty > 0 && item.freeOffer && (
        <div className="flex items-center gap-2">
          <Badge className="text-sxs h-5 w-7 rounded-sm bg-[#B6F5F959] px-0 py-0">
            {item.freeItemsQty}
          </Badge>
          <span className="flex-1 text-xs font-medium">
            {item.freeOffer.name}
          </span>
          <span className="text-xs font-medium text-green-700/90">Free</span>
        </div>
      )}
    </div>
  );
};
