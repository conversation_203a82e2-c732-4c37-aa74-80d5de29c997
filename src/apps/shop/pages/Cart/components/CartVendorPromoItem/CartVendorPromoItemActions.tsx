import { Button, Toolt<PERSON> } from '@mantine/core';
import MultiplyOperatorIcon from './assets/multiply-operator.svg?react';
import { AddToCartInput } from '@/libs/products/components/AddToCartInput/AddToCartInput';
import { getPriceString } from '@/utils';
import { Icon } from '@/libs/icons/Icon';
import { FEATURE_FLAGS } from '@/constants';

type CartVendorProductItemActionsProps = {
  quantity: number;
  offer: any;
  subtotal: number;
  subtotalWithoutDiscount: number;
  productOfferId: string;
  addToCart: (args: {
    productOfferId: string;
    quantity: number;
    onError: (msg: string) => void;
  }) => void;
  handleOpenRemoveProductModal: () => void;
};

export const CartVendorPromoItemActions = ({
  quantity,
  offer,
  subtotal,
  subtotalWithoutDiscount,
  productOfferId,
  addToCart,
  handleOpenRemoveProductModal,
}: CartVendorProductItemActionsProps) => (
  <div className="grid grid-cols-1">
    {quantity && (
      <>
        <div className="mb-1 flex items-end">
          <span className="text-left text-xs font-medium text-neutral-500">
            Unit Price:{' '}
            <span className="text-xs font-medium text-black">
              {getPriceString(offer.clinicPrice || offer.price)}
            </span>
          </span>
          <MultiplyOperatorIcon />
        </div>
        <AddToCartInput
          originalAmount={quantity}
          minIncrement={offer.increments}
          onUpdate={({ amount: newQuantity, setError }) =>
            addToCart({
              productOfferId,
              quantity: newQuantity,
              onError: (message) => {
                setError(message);
              },
            })
          }
        />
      </>
    )}

    <div className="mb-4 flex flex-row-reverse flex-wrap items-baseline">
      <span className="my-5 ml-1 text-xl leading-0 font-medium">
        {getPriceString(subtotal)}
      </span>
      {!(subtotal && subtotalWithoutDiscount > +subtotal) && (
        <span className="text-sm leading-0 text-neutral-400 line-through">
          $402,42
        </span>
      )}
    </div>
    <div className="flex items-center justify-end gap-4">
      {FEATURE_FLAGS.SAVED_ITEMS && (
        <>
          <Tooltip label="Save item for later">
            <Button variant="unstyled" aria-label="Save item for later">
              <Icon
                name="saveFlag"
                color="#667085"
                size="1rem"
                className="transition-colors hover:text-[#447bfd]"
                aria-hidden={true}
              />
            </Button>
          </Tooltip>
          <div className="h-4 w-px bg-[#D0D5DD]" />
        </>
      )}
      <Tooltip label="Remove item from cart">
        <Button
          onClick={handleOpenRemoveProductModal}
          variant="unstyled"
          aria-label="Remove item from cart"
        >
          <Icon name="trash" color="#667085" size="1rem" aria-hidden={true} />
        </Button>
      </Tooltip>
    </div>
  </div>
);
