import { Divider, Text } from '@mantine/core';
import type { CartItemType } from '@/libs/cart/types';
import type { PromoType } from '@/types/common';
import { CartVendorPromotionDisplay } from './CartVendorPromotionDisplay';
import { OfferType } from '@/types';

export type BuyXGetYPromotionData = {
  subtotalPaidItems: number;
  subtotalAllItems: number;
  paidItemsQty: number;
  freeItemsQty: number;
  freeOffer: OfferType | null;
  promotion: PromoType | null;
  items: (CartItemType & {
    freeItemsQty: number;
    freeOffer: OfferType | null;
  })[];
  imageUrl: string;
  manufacturer: string;
};

type CartVendorPromoItemProps = {
  promoItem: BuyXGetYPromotionData;
};

export const CartVendorPromoItem = ({
  promoItem,
}: CartVendorPromoItemProps) => {
  if (!promoItem || !promoItem.promotion || !promoItem.freeOffer) return null;

  return (
    <div className="flex w-full rounded-lg p-4">
      <div className="relative h-32 w-60 overflow-hidden rounded-lg border border-solid border-[#f2f2f2] bg-white p-0">
        <img
          src={promoItem.imageUrl}
          alt=""
          className="absolute top-[calc(50%+1rem)] left-1/2 max-h-[calc(100%-2rem)] w-full max-w-[calc(100%-2rem)] -translate-x-1/2 -translate-y-1/2 object-scale-down data-[fallback=true]:top-1/2"
        />
      </div>
      <div className="flex w-full gap-5">
        <div className="flex-1">
          <h3 className="max-w-9/10 text-sm leading-5 font-medium text-black">
            {promoItem.promotion.name}
          </h3>
          <div className="m-1 mb-3 flex text-center">
            <span className="text-xs text-neutral-500/80">
              SKU:
              <span className="ml-0.5 text-xs font-medium text-[#333333]">
                {promoItem.freeOffer.vendorSku}
              </span>
            </span>
            {promoItem.manufacturer ? (
              <>
                <Divider orientation="vertical" h="1rem" mx="md" />
                <Text ml="2px" c="#344054" size="xs">
                  {promoItem.manufacturer}
                </Text>
              </>
            ) : null}
          </div>
          <div className="divider-h"></div>
          {promoItem.items.map((item) => (
            <CartVendorPromotionDisplay key={item.id} item={item} />
          ))}
        </div>
        <div className="w-36">
          <div className="grid">
            {quantity && (
              <>
                <Flex>
                  <Text
                    c="rgba(102, 102, 102, 0.80)"
                    size="0.75rem"
                    ta="right"
                    w="100%"
                    mb="0.5rem"
                    fw="500"
                    className={styles.unitPrice}
                  >
                    Unit Price:{' '}
                    <Text size="0.75rem" c="#333" fw="500" span>
                      {getPriceString(offer.clinicPrice || offer.price)}
                    </Text>
                  </Text>
                  <MultiplyOperatorIcon />
                </Flex>
                <AddToCartInput
                  originalAmount={quantity}
                  minIncrement={offer.increments}
                  onUpdate={({ amount: newQuantity, setError }) =>
                    addToCart({
                      productOfferId,
                      quantity: newQuantity,
                      onError: (message) => {
                        setError(message);
                      },
                    })
                  }
                />
              </>
            )}

            <Flex
              justify="flex-end"
              align="flex-end"
              gap="10"
              mb="0.75rem"
              mt="0.5rem"
            >
              {subtotal && subtotalWithoutDiscount > +subtotal ? (
                <Text
                  size="sm"
                  fw="500"
                  td="line-through"
                  c="rgba(51, 51, 51, 0.50)"
                >
                  {getPriceString(subtotalWithoutDiscount)}
                </Text>
              ) : null}
              <Text size="1.5rem" fw="500">
                {getPriceString(subtotal)}
              </Text>
            </Flex>
            <div className="flex items-center justify-end gap-4">
              {FEATURE_FLAGS.SAVED_ITEMS && (
                <>
                  <Tooltip label="Save item for later">
                    <Button variant="unstyled" aria-label="Save item for later">
                      <Icon
                        name="saveFlag"
                        color="#667085"
                        size="1rem"
                        className="transition-colors hover:text-[#447bfd]"
                        aria-hidden={true}
                      />
                    </Button>
                  </Tooltip>
                  <div className="h-4 w-px bg-[#D0D5DD]" />
                </>
              )}
              <Tooltip label="Remove item from cart">
                <Button
                  onClick={handleOpenRemoveProductModal}
                  variant="unstyled"
                  aria-label="Remove item from cart"
                >
                  <Icon
                    name="trash"
                    color="#667085"
                    size="1rem"
                    aria-hidden={true}
                  />
                </Button>
              </Tooltip>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
