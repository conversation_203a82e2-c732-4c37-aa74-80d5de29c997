import { VendorSwap } from '@/libs/products/components/VendorSwap/VendorSwap';
import { PurchaseHistory } from '@/libs/products/components/PurchaseHistory/PurchaseHistory';

type CartVendorProductHistoryDisplayProps = {
  product: any;
  productOfferId: string;
  offer: any;
  handleSelectSwapVendor: (value: string | null) => void;
};

export const CartVendorPromotionHistoryDisplay = ({
  product,
  productOfferId,
  offer,
  handleSelectSwapVendor,
}: CartVendorProductHistoryDisplayProps) => (
  <>
    <VendorSwap
      offers={product.offers}
      currentOfferId={productOfferId}
      onSwap={handleSelectSwapVendor}
    />
    <div className="my-3 w-full">
      <PurchaseHistory
        productId={offer.id}
        lastOrderedAt={offer.lastOrderedAt}
        lastOrderedQuantity={offer.lastOrderedQuantity}
      />
    </div>
  </>
);
