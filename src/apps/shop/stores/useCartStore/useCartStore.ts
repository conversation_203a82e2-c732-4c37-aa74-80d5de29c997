import { enableMapSet } from 'immer';
import { apiErrorNotification, createStore } from '@/utils';
import { devtools } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import { CartType } from '@/libs/cart/types';
import { deleteApi, get, post } from '@/libs/utils/api';
import { useClinicStore } from '@/apps/shop/stores/useClinicStore';
import { ApiErrorProps } from '@/types/utility';
import type { CheckoutResponseType } from './type';

enableMapSet();

type State = CartType & {
  isCartLoading: boolean;
  errorOnCartLoading: boolean;
  updatingProductIds: Set<string>;
};
type Actions = {
  fetchCart: VoidFunction;
  clearCart: VoidFunction;
  setProductUpdating: (productOfferId: string, isUpdating: boolean) => void;
  swapOfferCartItem: (itemId: string, productOfferId: string) => void;
  checkout: () => Promise<CheckoutResponseType>;
  addToCart: (params: {
    productOfferId: string;
    quantity: number;
    onError: (message: string) => void;
  }) => void;
};

export const INITIAL_STATE: State = {
  budget: null,
  vendors: [],
  isCartLoading: false,
  errorOnCartLoading: false,
  itemsCount: 0,
  uniqueItemsCount: 0,
  subtotal: '0',
  total: '0',
  updatingProductIds: new Set<string>(),
};

export const useCartStore = createStore<Actions & State>()(
  immer(
    devtools((set, getState) => ({
      ...INITIAL_STATE,
      fetchCart: async () => {
        set({ isCartLoading: true, errorOnCartLoading: false });

        const clinicId = useClinicStore.getState().clinic?.id;
        try {
          const response = {
            budget: {
              type: null,
              weekToDate: [
                {
                  category: 'COGS',
                  spent: '406.00',
                  spentPercentage: '0',
                  target: '0.00',
                  targetPercentage: '0',
                },
                {
                  category: 'GA',
                  spent: '0.00',
                  spentPercentage: '0',
                  target: '0.00',
                  targetPercentage: '0',
                },
              ],
              monthToDate: [
                {
                  category: 'COGS',
                  spent: '406.00',
                  spentPercentage: '0',
                  target: '0.00',
                  targetPercentage: '0',
                },
                {
                  category: 'GA',
                  spent: '0.00',
                  spentPercentage: '0',
                  target: '0.00',
                  targetPercentage: '0',
                },
              ],
            },
            subtotal: '405.50',
            total: '405.50',
            itemsCount: 1,
            uniqueItemsCount: 1,
            vendors: [
              {
                id: '9d7559b3-2608-4f66-a75a-1f1b77cd472d',
                name: 'Zoetis',
                imageUrl:
                  'http://localhost:8000/storage/vendor-images/zoetis.png',
                subtotal: '405.50',
                total: '405.50',
                shippingFee: '0.00',
                amountToFreeShipping: '-405.50',
                cutoffTime: '2PM PST',
                items: [
                  {
                    id: '9f83c760-5801-4376-b1aa-3a6f46a01af1',
                    quantity: 1,
                    price: '405.50',
                    subtotal: '405.50',
                    productOfferId: '9d7581ca-240d-4406-86c0-2e0cd16384f8',
                    product: {
                      id: '90ab7d2d-0293-4e04-bdaf-2ed12d374eca',
                      name: 'VETSCAN VSPRO FIBRINOGEN TEST 24 PACK',
                      imageUrl:
                        'https://www.victormedical.com/assets/1/26/DimRegular/1107021_L.jpg',
                      isFavorite: false,
                      manufacturer: null,
                      manufacturerSku: '1107021',
                      description: '',
                      attributes: [],
                      offers: [
                        {
                          id: '9d7581ca-240d-4406-86c0-2e0cd16384f8',
                          vendor: {
                            id: '9d7559b3-2608-4f66-a75a-1f1b77cd472d',
                            name: 'Zoetis',
                            imageUrl:
                              'http://localhost:8000/storage/vendor-images/zoetis.png',
                            type: 'manufacturer',
                          },
                          vendorSku: '10020157',
                          price: '405.50',
                          clinicPrice: null,
                          stockStatus: 'IN_STOCK',
                          lastOrderedAt: null,
                          lastOrderedQuantity: null,
                          increments: 1,
                          isRecommended: false,
                          rebatePercent: null,
                        },
                      ],
                      isHazardous: false,
                      requiresPrescription: false,
                      requiresColdShipping: false,
                      isControlledSubstance: false,
                      requiresPedigree: false,
                      nationalDrugCode: null,
                      promotions: [
                        {
                          id: '01985b8b-0128-73de-8a84-da79e359d873',
                          name: 'BOGO TEST',
                          type: 'buy_x_get_y',
                          description: null,
                          startedAt: '2025-07-03',
                          endedAt: '2025-08-02',
                          vendor: {
                            id: '9d7559b3-2608-4f66-a75a-1f1b77cd472d',
                            name: 'Zoetis',
                            imageUrl:
                              'http://localhost:8000/storage/vendor-images/zoetis.png',
                          },
                          offers: [
                            {
                              id: '9d7581ca-240d-4406-86c0-2e0cd16384f8',
                              name: 'VETSCAN VSPRO FIBRINOGEN TEST 24 PACK',
                              price: '405.50',
                              clinicPrice: null,
                              gpoSavings: 0,
                              vendorSavings: '0.00',
                              isRecommended: false,
                              isFavorite: false,
                              lastOrderedAt: null,
                              lastOrderedQuantity: null,
                              stockStatus: 'IN_STOCK',
                              vendorSku: '10020157',
                              increments: 1,
                              vendor: {
                                id: '9d7559b3-2608-4f66-a75a-1f1b77cd472d',
                                name: 'Zoetis',
                                imageUrl:
                                  'http://localhost:8000/storage/vendor-images/zoetis.png',
                              },
                              quantityInCart: 0,
                            },
                          ],
                          requirements: [
                            {
                              type: 'minimum_quantity',
                              description: 'Minimum quantity required',
                              minimumQuantity: '1',
                            },
                          ],
                          benefits: [
                            {
                              type: 'give_free_product',
                              description: 'Buy X Get Y promotion',
                              quantity: '1',
                              message: null,
                              freeProductOffer: null,
                            },
                          ],
                          keyword: 'promo:zoetis-buy_x_get_y-bogo-test',
                        },
                      ],
                    },
                    notes: null,
                    promotions: {
                      applicablePromotions: [
                        {
                          promotion: {
                            id: '01985b8b-0128-73de-8a84-da79e359d873',
                            name: 'BOGO TEST',
                            type: 'buy_x_get_y',
                            description: null,
                            startedAt: '2025-07-03',
                            endedAt: '2025-08-02',
                            vendor: {
                              id: '9d7559b3-2608-4f66-a75a-1f1b77cd472d',
                              name: 'Zoetis',
                              imageUrl:
                                'http://localhost:8000/storage/vendor-images/zoetis.png',
                            },
                            offers: [
                              {
                                id: '9d7581ca-240d-4406-86c0-2e0cd16384f8',
                                name: 'VETSCAN VSPRO FIBRINOGEN TEST 24 PACK',
                                price: '405.50',
                                clinicPrice: null,
                                gpoSavings: 0,
                                vendorSavings: '0.00',
                                isRecommended: false,
                                isFavorite: false,
                                lastOrderedAt: null,
                                lastOrderedQuantity: null,
                                stockStatus: 'IN_STOCK',
                                vendorSku: '10020157',
                                increments: 1,
                                vendor: {
                                  id: '9d7559b3-2608-4f66-a75a-1f1b77cd472d',
                                  name: 'Zoetis',
                                  imageUrl:
                                    'http://localhost:8000/storage/vendor-images/zoetis.png',
                                },
                                quantityInCart: 0,
                              },
                            ],
                            requirements: [
                              {
                                type: 'minimum_quantity',
                                description: 'Minimum quantity required',
                                minimumQuantity: '1',
                              },
                            ],
                            benefits: [
                              {
                                type: 'give_free_product',
                                description: 'Buy X Get Y promotion',
                                quantity: '1',
                                message: null,
                                freeProductOffer: null,
                              },
                            ],
                            keyword: 'promo:zoetis-buy_x_get_y-bogo-test',
                          },
                          missingRequirements: [],
                        },
                      ],
                      potentialPromotions: [],
                    },
                  },
                  {
                    id: '9f83c760-5801-4376-b1aa-3a6f46a01af1',
                    quantity: 1,
                    price: '405.50',
                    subtotal: '405.50',
                    productOfferId: '9d7581ca-240d-4406-86c0-2e0cd16384f8',
                    product: {
                      id: '90ab7d2d-0293-4e04-bdaf-2ed12d374eca',
                      name: 'VETSCAN VSPRO FIBRINOGEN TEST 24 PACK',
                      imageUrl:
                        'https://www.victormedical.com/assets/1/26/DimRegular/1107021_L.jpg',
                      isFavorite: false,
                      manufacturer: null,
                      manufacturerSku: '1107021',
                      description: '',
                      attributes: [],
                      offers: [
                        {
                          id: '9d7581ca-240d-4406-86c0-2e0cd16384f8',
                          vendor: {
                            id: '9d7559b3-2608-4f66-a75a-1f1b77cd472d',
                            name: 'Zoetis',
                            imageUrl:
                              'http://localhost:8000/storage/vendor-images/zoetis.png',
                            type: 'manufacturer',
                          },
                          vendorSku: '10020157',
                          price: '405.50',
                          clinicPrice: null,
                          stockStatus: 'IN_STOCK',
                          lastOrderedAt: null,
                          lastOrderedQuantity: null,
                          increments: 1,
                          isRecommended: false,
                          rebatePercent: null,
                        },
                      ],
                      isHazardous: false,
                      requiresPrescription: false,
                      requiresColdShipping: false,
                      isControlledSubstance: false,
                      requiresPedigree: false,
                      nationalDrugCode: null,
                      promotions: [
                        {
                          id: '01985b8b-0128-73de-8a84-da79e359d873',
                          name: 'BOGO TEST',
                          type: 'buy_x_get_y',
                          description: null,
                          startedAt: '2025-07-03',
                          endedAt: '2025-08-02',
                          vendor: {
                            id: '9d7559b3-2608-4f66-a75a-1f1b77cd472d',
                            name: 'Zoetis',
                            imageUrl:
                              'http://localhost:8000/storage/vendor-images/zoetis.png',
                          },
                          offers: [
                            {
                              id: '9d7581ca-240d-4406-86c0-2e0cd16384f8',
                              name: 'VETSCAN VSPRO FIBRINOGEN TEST 24 PACK',
                              price: '405.50',
                              clinicPrice: null,
                              gpoSavings: 0,
                              vendorSavings: '0.00',
                              isRecommended: false,
                              isFavorite: false,
                              lastOrderedAt: null,
                              lastOrderedQuantity: null,
                              stockStatus: 'IN_STOCK',
                              vendorSku: '10020157',
                              increments: 1,
                              vendor: {
                                id: '9d7559b3-2608-4f66-a75a-1f1b77cd472d',
                                name: 'Zoetis',
                                imageUrl:
                                  'http://localhost:8000/storage/vendor-images/zoetis.png',
                              },
                              quantityInCart: 0,
                            },
                          ],
                          requirements: [
                            {
                              type: 'minimum_quantity',
                              description: 'Minimum quantity required',
                              minimumQuantity: '1',
                            },
                          ],
                          benefits: [
                            {
                              type: 'give_free_product',
                              description: 'Buy X Get Y promotion',
                              quantity: '1',
                              message: null,
                              freeProductOffer: null,
                            },
                          ],
                          keyword: 'promo:zoetis-buy_x_get_y-bogo-test',
                        },
                      ],
                    },
                    notes: null,
                    promotions: {
                      applicablePromotions: [
                        {
                          promotion: {
                            id: '01985b8b-0128-73de-8a84-da79e359d873',
                            name: 'BOGO TEST',
                            type: 'buy_x_get_y',
                            description: null,
                            startedAt: '2025-07-03',
                            endedAt: '2025-08-02',
                            vendor: {
                              id: '9d7559b3-2608-4f66-a75a-1f1b77cd472d',
                              name: 'Zoetis',
                              imageUrl:
                                'http://localhost:8000/storage/vendor-images/zoetis.png',
                            },
                            offers: [
                              {
                                id: '9d7581ca-240d-4406-86c0-2e0cd16384f8',
                                name: 'VETSCAN VSPRO FIBRINOGEN TEST 24 PACK',
                                price: '405.50',
                                clinicPrice: null,
                                gpoSavings: 0,
                                vendorSavings: '0.00',
                                isRecommended: false,
                                isFavorite: false,
                                lastOrderedAt: null,
                                lastOrderedQuantity: null,
                                stockStatus: 'IN_STOCK',
                                vendorSku: '10020157',
                                increments: 1,
                                vendor: {
                                  id: '9d7559b3-2608-4f66-a75a-1f1b77cd472d',
                                  name: 'Zoetis',
                                  imageUrl:
                                    'http://localhost:8000/storage/vendor-images/zoetis.png',
                                },
                                quantityInCart: 0,
                              },
                            ],
                            requirements: [
                              {
                                type: 'minimum_quantity',
                                description: 'Minimum quantity required',
                                minimumQuantity: '1',
                              },
                            ],
                            benefits: [
                              {
                                type: 'give_free_product',
                                description: 'Buy X Get Y promotion',
                                quantity: '1',
                                message: null,
                                freeProductOffer: null,
                              },
                            ],
                            keyword: 'promo:zoetis-buy_x_get_y-bogo-test',
                          },
                          missingRequirements: [],
                        },
                      ],
                      potentialPromotions: [],
                    },
                  },
                ],
              },
            ],
            promotions: {
              eligiblePromotions: [
                {
                  promotion: {
                    id: '01985b8b-0128-73de-8a84-da79e359d873',
                    name: 'BOGO TEST',
                    type: 'buy_x_get_y',
                    description: null,
                    startedAt: '2025-07-03',
                    endedAt: '2025-08-02',
                    vendor: {
                      id: '9d7559b3-2608-4f66-a75a-1f1b77cd472d',
                      name: 'Zoetis',
                      imageUrl:
                        'http://localhost:8000/storage/vendor-images/zoetis.png',
                    },
                    offers: [
                      {
                        id: '9d7581ca-240d-4406-86c0-2e0cd16384f8',
                        name: 'VETSCAN VSPRO FIBRINOGEN TEST 24 PACK',
                        price: '405.50',
                        clinicPrice: null,
                        gpoSavings: 0,
                        vendorSavings: '0.00',
                        isRecommended: false,
                        isFavorite: false,
                        lastOrderedAt: null,
                        lastOrderedQuantity: null,
                        stockStatus: 'IN_STOCK',
                        vendorSku: '10020157',
                        increments: 1,
                        vendor: {
                          id: '9d7559b3-2608-4f66-a75a-1f1b77cd472d',
                          name: 'Zoetis',
                          imageUrl:
                            'http://localhost:8000/storage/vendor-images/zoetis.png',
                        },
                        quantityInCart: 0,
                      },
                    ],
                    requirements: [
                      {
                        type: 'minimum_quantity',
                        description: 'Minimum quantity required',
                        minimumQuantity: '1',
                      },
                    ],
                    benefits: [
                      {
                        type: 'give_free_product',
                        description: 'Buy X Get Y promotion',
                        quantity: '1',
                        message: null,
                        freeProductOffer: null,
                      },
                    ],
                    keyword: 'promo:zoetis-buy_x_get_y-bogo-test',
                  },
                  applicableItems: ['9f83c760-5801-4376-b1aa-3a6f46a01af1'],
                  missingRequirements: [],
                },
              ],
              itemsWithPromotions: {
                '9f83c76058014376B1aa3a6f46a01af1': [
                  {
                    promotionId: '01985b8b-0128-73de-8a84-da79e359d873',
                    promotionName: 'BOGO TEST',
                    promotionType: 'buy_x_get_y',
                    isEligible: true,
                  },
                ],
              },
            },
          };

          set(response);
        } catch (error) {
          // TODO: Handle it better
          set({ errorOnCartLoading: true });
          console.error(error);
        }

        set({ isCartLoading: false });
      },
      clearCart: async () => {
        set({ isCartLoading: false });
        const clinic = useClinicStore.getState().clinic;

        const { budget } = await deleteApi<{
          budget: CartType['budget'];
        }>({
          url: `/clinics/${clinic?.id}/cart`,
        });

        set({
          ...INITIAL_STATE,
          budget: budget,
        });
      },
      setProductUpdating: (productOfferId, isUpdating) => {
        const { updatingProductIds } = getState();

        if (isUpdating) {
          updatingProductIds.add(productOfferId);
        } else {
          updatingProductIds.delete(productOfferId);
        }

        set({
          isCartLoading: isUpdating,
          updatingProductIds: updatingProductIds,
        });
      },
      addToCart: async ({ productOfferId, quantity, onError }) => {
        const state = getState();

        const currentItem = state.vendors
          .flatMap((vendor) => vendor.items)
          .find((item) => item.productOfferId === productOfferId);

        if (currentItem?.quantity === quantity) {
          return;
        }

        state.setProductUpdating(productOfferId, true);

        try {
          const clinicId = useClinicStore.getState().clinic?.id;
          const response = await post<CartType>({
            url: `/clinics/${clinicId}/cart/cart-items`,
            method: 'PATCH',
            body: {
              productOfferId,
              quantity,
              notes: '',
            },
          });

          set(response);
        } catch (err) {
          const { data } = err as ApiErrorProps;
          apiErrorNotification(data.message);
          onError(data?.message ?? '');
        }

        getState().setProductUpdating(productOfferId, false);
      },
      checkout: async () => {
        const clinic = useClinicStore.getState().clinic;

        const data = await post<CheckoutResponseType>({
          url: `/clinics/${clinic?.id}/orders`,
          body: {
            isBillingSameAsShippingAddress: true,
            paymentMethod: 'INVOICE',
            shippingAddress: clinic?.shippingAddress,
            billingAddress: clinic?.billingAddress,
          },
        });

        return data;
      },
      swapOfferCartItem: async (itemId, productOfferId) => {
        try {
          const clinicId = useClinicStore.getState().clinic?.id;
          const response = await post<CartType>({
            url: `/clinics/${clinicId}/cart/cart-items/${itemId}`,
            method: 'PATCH',
            body: {
              productOfferId,
            },
          });

          set(response);
        } catch (err) {
          const { data } = err as ApiErrorProps;

          apiErrorNotification(data.message);
        }
      },
    })),
  ),
);
