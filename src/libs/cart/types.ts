import type { OfferType, ProductType } from '@/types';
import { BUDGET_TYPE } from './constants';
import { PromoType } from '@/types/common';

export interface BudgetType {
  type: keyof typeof BUDGET_TYPE;
  weeklyCogs: number;
  monthlyCogs: number;
  weeklyGa: number;
  monthlyGa: number;
  targetCogsPercent: number;
  targetGaPercent: number;
  avgTwoWeeksSales: number;
  monthToDateSales: number;
  updatedAt?: string;
  externalMonthlyCogs: number;
  externalWeeklyCogs: number;
  includeExternalData: boolean;
}

export interface CartItemType {
  id: string;
  notes: string | null;
  price: string | null;
  subtotal: string | null;
  quantity: number;
  productOfferId: string;
  product: ProductType;
}

export interface CartVendorType {
  subtotal: string;
  total: string;
  id: string;
  name: string;
  imageUrl: string;
  items: CartItemType[];
  shippingFee: string;
  amountToFreeShipping: string;
  cutoffTime: string | null;
  // vendorSavings: string;
  // gpoSavings: string;
}

export type CheckoutType = {
  totalVendors: number;
  totalItems: number;
  totalPrice: number;
} | null;

export interface CartType {
  vendors: CartVendorType[];
  budget: {
    type: keyof typeof BUDGET_TYPE;
    monthToDate: {
      // TODO: check category list
      category: string;
      spent: string;
      spentPercentage: string;
      target: string;
      targetPercentage: string;
    }[];
    weekToDate: {
      category: string;
      spent: string;
      spentPercentage: string;
      target: string;
      targetPercentage: string;
    }[];
  } | null;
  itemsCount: number;
  uniqueItemsCount: number;
  subtotal: string;
  total: string;
}

export type BuyXGetYPromotionData = {
  subtotalPaidItems: number;
  subtotalAllItems: number;
  paidItemsQty: number;
  freeItemsQty: number;
  freeOffer: OfferType | null;
  promotion: PromoType | null;
  items: (CartItemType & {
    freeItemsQty: number;
    freeOffer: OfferType | null;
  })[];
  imageUrl: string;
  manufacturer: string;
};
